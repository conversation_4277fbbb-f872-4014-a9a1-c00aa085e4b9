/*
Copyright 2020 The Kruise Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"net"
	"testing"

	utilnet "k8s.io/apimachinery/pkg/util/net"
)

func TestResolveWebhookBindAddress(t *testing.T) {
	// Test that ResolveBindAddress returns a valid IP address
	bindIP, err := utilnet.ResolveBindAddress(nil)
	if err != nil {
		t.Fatalf("ResolveBindAddress failed: %v", err)
	}

	if bindIP == nil {
		t.Fatal("ResolveBindAddress returned nil IP")
	}

	// Verify that the returned IP is not the all-zero address
	if bindIP.String() == "0.0.0.0" || bindIP.String() == "::" {
		t.Errorf("ResolveBindAddress returned all-zero address: %s", bindIP.String())
	}

	// Verify that the returned IP is a valid IP address
	if bindIP.To4() == nil && bindIP.To16() == nil {
		t.Errorf("ResolveBindAddress returned invalid IP: %s", bindIP.String())
	}

	t.Logf("ResolveBindAddress returned: %s", bindIP.String())
}

func TestResolveBindAddressWithLoopback(t *testing.T) {
	// Test with loopback address - should return a non-loopback address
	loopbackIP := net.ParseIP("127.0.0.1")
	bindIP, err := utilnet.ResolveBindAddress(loopbackIP)
	if err != nil {
		t.Fatalf("ResolveBindAddress with loopback failed: %v", err)
	}

	if bindIP == nil {
		t.Fatal("ResolveBindAddress returned nil IP")
	}

	// Should not return the loopback address itself
	if bindIP.IsLoopback() {
		t.Logf("Warning: ResolveBindAddress returned loopback address: %s (this may be expected in some test environments)", bindIP.String())
	}

	t.Logf("ResolveBindAddress with loopback input returned: %s", bindIP.String())
}

func TestResolveBindAddressWithUnspecified(t *testing.T) {
	// Test with unspecified address (0.0.0.0) - should return a specific address
	unspecifiedIP := net.ParseIP("0.0.0.0")
	bindIP, err := utilnet.ResolveBindAddress(unspecifiedIP)
	if err != nil {
		t.Fatalf("ResolveBindAddress with unspecified failed: %v", err)
	}

	if bindIP == nil {
		t.Fatal("ResolveBindAddress returned nil IP")
	}

	// Should not return the unspecified address
	if bindIP.IsUnspecified() {
		t.Errorf("ResolveBindAddress returned unspecified address: %s", bindIP.String())
	}

	t.Logf("ResolveBindAddress with unspecified input returned: %s", bindIP.String())
}

func TestResolveBindAddressWithSpecificIP(t *testing.T) {
	// Test with a specific IP - should return the same IP
	specificIP := net.ParseIP("*************")
	bindIP, err := utilnet.ResolveBindAddress(specificIP)
	if err != nil {
		t.Fatalf("ResolveBindAddress with specific IP failed: %v", err)
	}

	if bindIP == nil {
		t.Fatal("ResolveBindAddress returned nil IP")
	}

	// Should return the same IP when given a specific non-loopback, non-unspecified IP
	if !bindIP.Equal(specificIP) {
		t.Logf("ResolveBindAddress with specific IP %s returned different IP: %s (this may be expected if the IP is not available on this host)", specificIP.String(), bindIP.String())
	}

	t.Logf("ResolveBindAddress with specific IP %s returned: %s", specificIP.String(), bindIP.String())
}
