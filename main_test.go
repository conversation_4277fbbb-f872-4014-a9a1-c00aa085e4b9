package main

import (
	"os"
	"testing"

	webhookutil "github.com/openkruise/kruise/pkg/webhook/util"
)

func TestMain(t *testing.T) {
	// Basic test to ensure main package compiles
	t.Log("Main package test passed")
}

func TestWebhookHostConfiguration(t *testing.T) {
	tests := []struct {
		name           string
		webhookHostEnv string
		expectedHost   string
		description    string
	}{
		{
			name:           "default_behavior",
			webhookHostEnv: "",
			expectedHost:   "",
			description:    "Should bind to all interfaces when WEBHOOK_HOST is not set",
		},
		{
			name:           "specific_host",
			webhookHostEnv: "127.0.0.1",
			expectedHost:   "127.0.0.1",
			description:    "Should use specific host when WEBHOOK_HOST is set",
		},
		{
			name:           "specific_ip",
			webhookHostEnv: "*************",
			expectedHost:   "*************",
			description:    "Should use specific IP when WEBHOOK_HOST is set",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Save original environment
			originalHost := os.Getenv("WEBHOOK_HOST")
			defer func() {
				if originalHost != "" {
					os.Setenv("WEBHOOK_HOST", originalHost)
				} else {
					os.Unsetenv("WEBHOOK_HOST")
				}
			}()

			// Set test environment
			if tt.webhookHostEnv != "" {
				os.Setenv("WEBHOOK_HOST", tt.webhookHostEnv)
			} else {
				os.Unsetenv("WEBHOOK_HOST")
			}

			// Test the webhook host determination logic
			var webhookHost string
			if envHost := webhookutil.GetHost(); envHost != "" {
				webhookHost = envHost
			} else {
				webhookHost = ""
			}

			if webhookHost != tt.expectedHost {
				t.Errorf("Expected webhook host %q, got %q. %s", tt.expectedHost, webhookHost, tt.description)
			}

			t.Logf("✓ %s: webhook host = %q", tt.description, webhookHost)
		})
	}
}
